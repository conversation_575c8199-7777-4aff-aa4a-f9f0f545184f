{"version": 3, "names": ["_helperCompilationTargets", "data", "require", "_options", "msg", "loc", "type", "parent", "name", "index", "JSON", "stringify", "Error", "access", "assertRootMode", "value", "undefined", "assertSourceMaps", "assertCompact", "assertSourceType", "assertCallerMetadata", "obj", "assertObject", "prop", "Object", "keys", "propLoc", "assertInputSourceMap", "assertString", "assertFunction", "assertBoolean", "Array", "isArray", "assertArray", "assertIgnoreList", "arr", "for<PERSON>ach", "item", "i", "assertIgnoreItem", "RegExp", "assertConfigApplicableTest", "checkValidTest", "assertConfigFileSearch", "assertBabelrcSearch", "assertPluginList", "assertPluginItem", "length", "assertPlugin<PERSON>arget", "opts", "assertTargets", "isBrowsersQueryValid", "browsersLoc", "esmodulesLoc", "assertBrowsersList", "browsers", "<PERSON><PERSON><PERSON><PERSON>", "key", "val", "subLoc", "hasOwnProperty", "call", "TargetNames", "validTargets", "join", "assertBrowserVersion", "Math", "round", "assertAssumptions", "root", "inPreset", "source", "assumptionsNames", "has"], "sources": ["../../../src/config/validation/option-assertions.ts"], "sourcesContent": ["import {\n  isBrowsers<PERSON>ueryValid,\n  TargetNames,\n} from \"@babel/helper-compilation-targets\";\n\nimport type {\n  ConfigFileSearch,\n  BabelrcSearch,\n  IgnoreList,\n  IgnoreItem,\n  PluginList,\n  PluginItem,\n  PluginTarget,\n  ConfigApplicableTest,\n  SourceMapsOption,\n  SourceTypeOption,\n  CompactOption,\n  RootInputSourceMapOption,\n  NestingPath,\n  CallerMetadata,\n  RootMode,\n  TargetsListOrObject,\n  AssumptionName,\n} from \"./options.ts\";\n\nimport { assumptionsNames } from \"./options.ts\";\n\nexport type { RootPath } from \"./options.ts\";\n\nexport type ValidatorSet = {\n  [name: string]: Validator<any>;\n};\n\nexport type Validator<T> = (loc: OptionPath, value: unknown) => T;\n\nexport function msg(loc: NestingPath | GeneralPath): string {\n  switch (loc.type) {\n    case \"root\":\n      return ``;\n    case \"env\":\n      return `${msg(loc.parent)}.env[\"${loc.name}\"]`;\n    case \"overrides\":\n      return `${msg(loc.parent)}.overrides[${loc.index}]`;\n    case \"option\":\n      return `${msg(loc.parent)}.${loc.name}`;\n    case \"access\":\n      return `${msg(loc.parent)}[${JSON.stringify(loc.name)}]`;\n    default:\n      // @ts-expect-error should not happen when code is type checked\n      throw new Error(`Assertion failure: Unknown type ${loc.type}`);\n  }\n}\n\nexport function access(loc: GeneralPath, name: string | number): AccessPath {\n  return {\n    type: \"access\",\n    name,\n    parent: loc,\n  };\n}\n\nexport type OptionPath = Readonly<{\n  type: \"option\";\n  name: string;\n  parent: NestingPath;\n}>;\ntype AccessPath = Readonly<{\n  type: \"access\";\n  name: string | number;\n  parent: GeneralPath;\n}>;\ntype GeneralPath = OptionPath | AccessPath;\n\nexport function assertRootMode(\n  loc: OptionPath,\n  value: unknown,\n): RootMode | void {\n  if (\n    value !== undefined &&\n    value !== \"root\" &&\n    value !== \"upward\" &&\n    value !== \"upward-optional\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a \"root\", \"upward\", \"upward-optional\" or undefined`,\n    );\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertSourceMaps(\n  loc: OptionPath,\n  value: unknown,\n): SourceMapsOption | void {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    value !== \"inline\" &&\n    value !== \"both\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a boolean, \"inline\", \"both\", or undefined`,\n    );\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertCompact(\n  loc: OptionPath,\n  value: unknown,\n): CompactOption | void {\n  if (value !== undefined && typeof value !== \"boolean\" && value !== \"auto\") {\n    throw new Error(`${msg(loc)} must be a boolean, \"auto\", or undefined`);\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertSourceType(\n  loc: OptionPath,\n  value: unknown,\n): SourceTypeOption | void {\n  if (\n    value !== undefined &&\n    value !== \"module\" &&\n    value !== \"commonjs\" &&\n    value !== \"script\" &&\n    value !== \"unambiguous\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be \"module\", \"commonjs\", \"script\", \"unambiguous\", or undefined`,\n    );\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertCallerMetadata(\n  loc: OptionPath,\n  value: unknown,\n): CallerMetadata | undefined {\n  const obj = assertObject(loc, value);\n  if (obj) {\n    if (typeof obj.name !== \"string\") {\n      throw new Error(\n        `${msg(loc)} set but does not contain \"name\" property string`,\n      );\n    }\n\n    for (const prop of Object.keys(obj)) {\n      const propLoc = access(loc, prop);\n      const value = obj[prop];\n      if (\n        value != null &&\n        typeof value !== \"boolean\" &&\n        typeof value !== \"string\" &&\n        typeof value !== \"number\"\n      ) {\n        // NOTE(logan): I'm limiting the type here so that we can guarantee that\n        // the \"caller\" value will serialize to JSON nicely. We can always\n        // allow more complex structures later though.\n        throw new Error(\n          `${msg(\n            propLoc,\n          )} must be null, undefined, a boolean, a string, or a number.`,\n        );\n      }\n    }\n  }\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\n\nexport function assertInputSourceMap(\n  loc: OptionPath,\n  value: unknown,\n): RootInputSourceMapOption {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    (typeof value !== \"object\" || !value)\n  ) {\n    throw new Error(`${msg(loc)} must be a boolean, object, or undefined`);\n  }\n  return value as RootInputSourceMapOption;\n}\n\nexport function assertString(loc: GeneralPath, value: unknown): string | void {\n  if (value !== undefined && typeof value !== \"string\") {\n    throw new Error(`${msg(loc)} must be a string, or undefined`);\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertFunction(\n  loc: GeneralPath,\n  value: unknown,\n): Function | void {\n  if (value !== undefined && typeof value !== \"function\") {\n    throw new Error(`${msg(loc)} must be a function, or undefined`);\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertBoolean(\n  loc: GeneralPath,\n  value: unknown,\n): boolean | void {\n  if (value !== undefined && typeof value !== \"boolean\") {\n    throw new Error(`${msg(loc)} must be a boolean, or undefined`);\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertObject(\n  loc: GeneralPath,\n  value: unknown,\n): { readonly [key: string]: unknown } | void {\n  if (\n    value !== undefined &&\n    (typeof value !== \"object\" || Array.isArray(value) || !value)\n  ) {\n    throw new Error(`${msg(loc)} must be an object, or undefined`);\n  }\n  // @ts-expect-error todo(flow->ts) value is still typed as unknown, also assert function typically should not return a value\n  return value;\n}\n\nexport function assertArray<T>(\n  loc: GeneralPath,\n  value: Array<T> | undefined | null,\n): ReadonlyArray<T> | undefined | null {\n  if (value != null && !Array.isArray(value)) {\n    throw new Error(`${msg(loc)} must be an array, or undefined`);\n  }\n  return value;\n}\n\nexport function assertIgnoreList(\n  loc: OptionPath,\n  value: unknown[] | undefined,\n): IgnoreList | void {\n  const arr = assertArray(loc, value);\n  arr?.forEach((item, i) => assertIgnoreItem(access(loc, i), item));\n  // @ts-expect-error todo(flow->ts)\n  return arr;\n}\nfunction assertIgnoreItem(loc: GeneralPath, value: unknown): IgnoreItem {\n  if (\n    typeof value !== \"string\" &&\n    typeof value !== \"function\" &&\n    !(value instanceof RegExp)\n  ) {\n    throw new Error(\n      `${msg(\n        loc,\n      )} must be an array of string/Function/RegExp values, or undefined`,\n    );\n  }\n  return value as IgnoreItem;\n}\n\nexport function assertConfigApplicableTest(\n  loc: OptionPath,\n  value: unknown,\n): ConfigApplicableTest | void {\n  if (value === undefined) {\n    // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n    return value;\n  }\n\n  if (Array.isArray(value)) {\n    value.forEach((item, i) => {\n      if (!checkValidTest(item)) {\n        throw new Error(\n          `${msg(access(loc, i))} must be a string/Function/RegExp.`,\n        );\n      }\n    });\n  } else if (!checkValidTest(value)) {\n    throw new Error(\n      `${msg(loc)} must be a string/Function/RegExp, or an array of those`,\n    );\n  }\n  return value as ConfigApplicableTest;\n}\n\nfunction checkValidTest(value: unknown): value is string | Function | RegExp {\n  return (\n    typeof value === \"string\" ||\n    typeof value === \"function\" ||\n    value instanceof RegExp\n  );\n}\n\nexport function assertConfigFileSearch(\n  loc: OptionPath,\n  value: unknown,\n): ConfigFileSearch | void {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    typeof value !== \"string\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a undefined, a boolean, a string, ` +\n        `got ${JSON.stringify(value)}`,\n    );\n  }\n  // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n  return value;\n}\n\nexport function assertBabelrcSearch(\n  loc: OptionPath,\n  value: unknown,\n): BabelrcSearch | void {\n  if (value === undefined || typeof value === \"boolean\") {\n    // @ts-expect-error: TS can only narrow down the type when \"strictNullCheck\" is true\n    return value;\n  }\n\n  if (Array.isArray(value)) {\n    value.forEach((item, i) => {\n      if (!checkValidTest(item)) {\n        throw new Error(\n          `${msg(access(loc, i))} must be a string/Function/RegExp.`,\n        );\n      }\n    });\n  } else if (!checkValidTest(value)) {\n    throw new Error(\n      `${msg(loc)} must be a undefined, a boolean, a string/Function/RegExp ` +\n        `or an array of those, got ${JSON.stringify(value as any)}`,\n    );\n  }\n  return value as BabelrcSearch;\n}\n\nexport function assertPluginList(\n  loc: OptionPath,\n  value: unknown[] | null | undefined,\n): PluginList | void {\n  const arr = assertArray(loc, value);\n  if (arr) {\n    // Loop instead of using `.map` in order to preserve object identity\n    // for plugin array for use during config chain processing.\n    arr.forEach((item, i) => assertPluginItem(access(loc, i), item));\n  }\n  return arr as any;\n}\nfunction assertPluginItem(loc: GeneralPath, value: unknown): PluginItem {\n  if (Array.isArray(value)) {\n    if (value.length === 0) {\n      throw new Error(`${msg(loc)} must include an object`);\n    }\n\n    if (value.length > 3) {\n      throw new Error(`${msg(loc)} may only be a two-tuple or three-tuple`);\n    }\n\n    assertPluginTarget(access(loc, 0), value[0]);\n\n    if (value.length > 1) {\n      const opts = value[1];\n      if (\n        opts !== undefined &&\n        opts !== false &&\n        (typeof opts !== \"object\" || Array.isArray(opts) || opts === null)\n      ) {\n        throw new Error(\n          `${msg(access(loc, 1))} must be an object, false, or undefined`,\n        );\n      }\n    }\n    if (value.length === 3) {\n      const name = value[2];\n      if (name !== undefined && typeof name !== \"string\") {\n        throw new Error(\n          `${msg(access(loc, 2))} must be a string, or undefined`,\n        );\n      }\n    }\n  } else {\n    assertPluginTarget(loc, value);\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\nfunction assertPluginTarget(loc: GeneralPath, value: unknown): PluginTarget {\n  if (\n    (typeof value !== \"object\" || !value) &&\n    typeof value !== \"string\" &&\n    typeof value !== \"function\"\n  ) {\n    throw new Error(`${msg(loc)} must be a string, object, function`);\n  }\n  return value;\n}\n\nexport function assertTargets(\n  loc: GeneralPath,\n  value: any,\n): TargetsListOrObject {\n  if (isBrowsersQueryValid(value)) return value;\n\n  if (typeof value !== \"object\" || !value || Array.isArray(value)) {\n    throw new Error(\n      `${msg(loc)} must be a string, an array of strings or an object`,\n    );\n  }\n\n  const browsersLoc = access(loc, \"browsers\");\n  const esmodulesLoc = access(loc, \"esmodules\");\n\n  assertBrowsersList(browsersLoc, value.browsers);\n  assertBoolean(esmodulesLoc, value.esmodules);\n\n  for (const key of Object.keys(value)) {\n    const val = value[key];\n    const subLoc = access(loc, key);\n\n    if (key === \"esmodules\") assertBoolean(subLoc, val);\n    else if (key === \"browsers\") assertBrowsersList(subLoc, val);\n    else if (!Object.hasOwn(TargetNames, key)) {\n      const validTargets = Object.keys(TargetNames).join(\", \");\n      throw new Error(\n        `${msg(\n          subLoc,\n        )} is not a valid target. Supported targets are ${validTargets}`,\n      );\n    } else assertBrowserVersion(subLoc, val);\n  }\n\n  return value;\n}\n\nfunction assertBrowsersList(loc: GeneralPath, value: unknown) {\n  if (value !== undefined && !isBrowsersQueryValid(value)) {\n    throw new Error(\n      `${msg(loc)} must be undefined, a string or an array of strings`,\n    );\n  }\n}\n\nfunction assertBrowserVersion(loc: GeneralPath, value: unknown) {\n  if (typeof value === \"number\" && Math.round(value) === value) return;\n  if (typeof value === \"string\") return;\n\n  throw new Error(`${msg(loc)} must be a string or an integer number`);\n}\n\nexport function assertAssumptions(\n  loc: GeneralPath,\n  value: { [key: string]: unknown },\n): { [name: string]: boolean } | void {\n  if (value === undefined) return;\n\n  if (typeof value !== \"object\" || value === null) {\n    throw new Error(`${msg(loc)} must be an object or undefined.`);\n  }\n\n  // todo(flow->ts): remove any\n  let root: any = loc;\n  do {\n    root = root.parent;\n  } while (root.type !== \"root\");\n  const inPreset = root.source === \"preset\";\n\n  for (const name of Object.keys(value)) {\n    const subLoc = access(loc, name);\n    if (!assumptionsNames.has(name as AssumptionName)) {\n      throw new Error(`${msg(subLoc)} is not a supported assumption.`);\n    }\n    if (typeof value[name] !== \"boolean\") {\n      throw new Error(`${msg(subLoc)} must be a boolean.`);\n    }\n    if (inPreset && value[name] === false) {\n      throw new Error(\n        `${msg(subLoc)} cannot be set to 'false' inside presets.`,\n      );\n    }\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,0BAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,yBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAyBA,IAAAE,QAAA,GAAAD,OAAA;AAUO,SAASE,GAAGA,CAACC,GAA8B,EAAU;EAC1D,QAAQA,GAAG,CAACC,IAAI;IACd,KAAK,MAAM;MACT,OAAO,EAAE;IACX,KAAK,KAAK;MACR,OAAO,GAAGF,GAAG,CAACC,GAAG,CAACE,MAAM,CAAC,SAASF,GAAG,CAACG,IAAI,IAAI;IAChD,KAAK,WAAW;MACd,OAAO,GAAGJ,GAAG,CAACC,GAAG,CAACE,MAAM,CAAC,cAAcF,GAAG,CAACI,KAAK,GAAG;IACrD,KAAK,QAAQ;MACX,OAAO,GAAGL,GAAG,CAACC,GAAG,CAACE,MAAM,CAAC,IAAIF,GAAG,CAACG,IAAI,EAAE;IACzC,KAAK,QAAQ;MACX,OAAO,GAAGJ,GAAG,CAACC,GAAG,CAACE,MAAM,CAAC,IAAIG,IAAI,CAACC,SAAS,CAACN,GAAG,CAACG,IAAI,CAAC,GAAG;IAC1D;MAEE,MAAM,IAAII,KAAK,CAAC,mCAAmCP,GAAG,CAACC,IAAI,EAAE,CAAC;EAClE;AACF;AAEO,SAASO,MAAMA,CAACR,GAAgB,EAAEG,IAAqB,EAAc;EAC1E,OAAO;IACLF,IAAI,EAAE,QAAQ;IACdE,IAAI;IACJD,MAAM,EAAEF;EACV,CAAC;AACH;AAcO,SAASS,cAAcA,CAC5BT,GAAe,EACfU,KAAc,EACG;EACjB,IACEA,KAAK,KAAKC,SAAS,IACnBD,KAAK,KAAK,MAAM,IAChBA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,iBAAiB,EAC3B;IACA,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,6DACb,CAAC;EACH;EAEA,OAAOU,KAAK;AACd;AAEO,SAASE,gBAAgBA,CAC9BZ,GAAe,EACfU,KAAc,EACW;EACzB,IACEA,KAAK,KAAKC,SAAS,IACnB,OAAOD,KAAK,KAAK,SAAS,IAC1BA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,MAAM,EAChB;IACA,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,oDACb,CAAC;EACH;EAEA,OAAOU,KAAK;AACd;AAEO,SAASG,aAAaA,CAC3Bb,GAAe,EACfU,KAAc,EACQ;EACtB,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,MAAM,EAAE;IACzE,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACxE;EAEA,OAAOU,KAAK;AACd;AAEO,SAASI,gBAAgBA,CAC9Bd,GAAe,EACfU,KAAc,EACW;EACzB,IACEA,KAAK,KAAKC,SAAS,IACnBD,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,UAAU,IACpBA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,aAAa,EACvB;IACA,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,sEACb,CAAC;EACH;EAEA,OAAOU,KAAK;AACd;AAEO,SAASK,oBAAoBA,CAClCf,GAAe,EACfU,KAAc,EACc;EAC5B,MAAMM,GAAG,GAAGC,YAAY,CAACjB,GAAG,EAAEU,KAAK,CAAC;EACpC,IAAIM,GAAG,EAAE;IACP,IAAI,OAAOA,GAAG,CAACb,IAAI,KAAK,QAAQ,EAAE;MAChC,MAAM,IAAII,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,kDACb,CAAC;IACH;IAEA,KAAK,MAAMkB,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,EAAE;MACnC,MAAMK,OAAO,GAAGb,MAAM,CAACR,GAAG,EAAEkB,IAAI,CAAC;MACjC,MAAMR,KAAK,GAAGM,GAAG,CAACE,IAAI,CAAC;MACvB,IACER,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,KAAK,SAAS,IAC1B,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,QAAQ,EACzB;QAIA,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CACJsB,OACF,CAAC,6DACH,CAAC;MACH;IACF;EACF;EAEA,OAAOX,KAAK;AACd;AAEO,SAASY,oBAAoBA,CAClCtB,GAAe,EACfU,KAAc,EACY;EAC1B,IACEA,KAAK,KAAKC,SAAS,IACnB,OAAOD,KAAK,KAAK,SAAS,KACzB,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAAC,EACrC;IACA,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACxE;EACA,OAAOU,KAAK;AACd;AAEO,SAASa,YAAYA,CAACvB,GAAgB,EAAEU,KAAc,EAAiB;EAC5E,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IACpD,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAC/D;EAEA,OAAOU,KAAK;AACd;AAEO,SAASc,cAAcA,CAC5BxB,GAAgB,EAChBU,KAAc,EACG;EACjB,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IACtD,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,mCAAmC,CAAC;EACjE;EAEA,OAAOU,KAAK;AACd;AAEO,SAASe,aAAaA,CAC3BzB,GAAgB,EAChBU,KAAc,EACE;EAChB,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,SAAS,EAAE;IACrD,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAChE;EAEA,OAAOU,KAAK;AACd;AAEO,SAASO,YAAYA,CAC1BjB,GAAgB,EAChBU,KAAc,EAC8B;EAC5C,IACEA,KAAK,KAAKC,SAAS,KAClB,OAAOD,KAAK,KAAK,QAAQ,IAAIgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC,EAC7D;IACA,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAChE;EAEA,OAAOU,KAAK;AACd;AAEO,SAASkB,WAAWA,CACzB5B,GAAgB,EAChBU,KAAkC,EACG;EACrC,IAAIA,KAAK,IAAI,IAAI,IAAI,CAACgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE;IAC1C,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAC/D;EACA,OAAOU,KAAK;AACd;AAEO,SAASmB,gBAAgBA,CAC9B7B,GAAe,EACfU,KAA4B,EACT;EACnB,MAAMoB,GAAG,GAAGF,WAAW,CAAC5B,GAAG,EAAEU,KAAK,CAAC;EACnCoB,GAAG,YAAHA,GAAG,CAAEC,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAKC,gBAAgB,CAAC1B,MAAM,CAACR,GAAG,EAAEiC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC;EAEjE,OAAOF,GAAG;AACZ;AACA,SAASI,gBAAgBA,CAAClC,GAAgB,EAAEU,KAAc,EAAc;EACtE,IACE,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,UAAU,IAC3B,EAAEA,KAAK,YAAYyB,MAAM,CAAC,EAC1B;IACA,MAAM,IAAI5B,KAAK,CACb,GAAGR,GAAG,CACJC,GACF,CAAC,kEACH,CAAC;EACH;EACA,OAAOU,KAAK;AACd;AAEO,SAAS0B,0BAA0BA,CACxCpC,GAAe,EACfU,KAAc,EACe;EAC7B,IAAIA,KAAK,KAAKC,SAAS,EAAE;IAEvB,OAAOD,KAAK;EACd;EAEA,IAAIgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE;IACxBA,KAAK,CAACqB,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACzB,IAAI,CAACI,cAAc,CAACL,IAAI,CAAC,EAAE;QACzB,MAAM,IAAIzB,KAAK,CACb,GAAGR,GAAG,CAACS,MAAM,CAACR,GAAG,EAAEiC,CAAC,CAAC,CAAC,oCACxB,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,CAACI,cAAc,CAAC3B,KAAK,CAAC,EAAE;IACjC,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,yDACb,CAAC;EACH;EACA,OAAOU,KAAK;AACd;AAEA,SAAS2B,cAAcA,CAAC3B,KAAc,EAAuC;EAC3E,OACE,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,UAAU,IAC3BA,KAAK,YAAYyB,MAAM;AAE3B;AAEO,SAASG,sBAAsBA,CACpCtC,GAAe,EACfU,KAAc,EACW;EACzB,IACEA,KAAK,KAAKC,SAAS,IACnB,OAAOD,KAAK,KAAK,SAAS,IAC1B,OAAOA,KAAK,KAAK,QAAQ,EACzB;IACA,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,6CAA6C,GACtD,OAAOK,IAAI,CAACC,SAAS,CAACI,KAAK,CAAC,EAChC,CAAC;EACH;EAEA,OAAOA,KAAK;AACd;AAEO,SAAS6B,mBAAmBA,CACjCvC,GAAe,EACfU,KAAc,EACQ;EACtB,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,SAAS,EAAE;IAErD,OAAOA,KAAK;EACd;EAEA,IAAIgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE;IACxBA,KAAK,CAACqB,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACzB,IAAI,CAACI,cAAc,CAACL,IAAI,CAAC,EAAE;QACzB,MAAM,IAAIzB,KAAK,CACb,GAAGR,GAAG,CAACS,MAAM,CAACR,GAAG,EAAEiC,CAAC,CAAC,CAAC,oCACxB,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,CAACI,cAAc,CAAC3B,KAAK,CAAC,EAAE;IACjC,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,4DAA4D,GACrE,6BAA6BK,IAAI,CAACC,SAAS,CAACI,KAAY,CAAC,EAC7D,CAAC;EACH;EACA,OAAOA,KAAK;AACd;AAEO,SAAS8B,gBAAgBA,CAC9BxC,GAAe,EACfU,KAAmC,EAChB;EACnB,MAAMoB,GAAG,GAAGF,WAAW,CAAC5B,GAAG,EAAEU,KAAK,CAAC;EACnC,IAAIoB,GAAG,EAAE;IAGPA,GAAG,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAKQ,gBAAgB,CAACjC,MAAM,CAACR,GAAG,EAAEiC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC;EAClE;EACA,OAAOF,GAAG;AACZ;AACA,SAASW,gBAAgBA,CAACzC,GAAgB,EAAEU,KAAc,EAAc;EACtE,IAAIgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE;IACxB,IAAIA,KAAK,CAACgC,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAInC,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACvD;IAEA,IAAIU,KAAK,CAACgC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAInC,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACvE;IAEA2C,kBAAkB,CAACnC,MAAM,CAACR,GAAG,EAAE,CAAC,CAAC,EAAEU,KAAK,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAIA,KAAK,CAACgC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAME,IAAI,GAAGlC,KAAK,CAAC,CAAC,CAAC;MACrB,IACEkC,IAAI,KAAKjC,SAAS,IAClBiC,IAAI,KAAK,KAAK,KACb,OAAOA,IAAI,KAAK,QAAQ,IAAIlB,KAAK,CAACC,OAAO,CAACiB,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,CAAC,EAClE;QACA,MAAM,IAAIrC,KAAK,CACb,GAAGR,GAAG,CAACS,MAAM,CAACR,GAAG,EAAE,CAAC,CAAC,CAAC,yCACxB,CAAC;MACH;IACF;IACA,IAAIU,KAAK,CAACgC,MAAM,KAAK,CAAC,EAAE;MACtB,MAAMvC,IAAI,GAAGO,KAAK,CAAC,CAAC,CAAC;MACrB,IAAIP,IAAI,KAAKQ,SAAS,IAAI,OAAOR,IAAI,KAAK,QAAQ,EAAE;QAClD,MAAM,IAAII,KAAK,CACb,GAAGR,GAAG,CAACS,MAAM,CAACR,GAAG,EAAE,CAAC,CAAC,CAAC,iCACxB,CAAC;MACH;IACF;EACF,CAAC,MAAM;IACL2C,kBAAkB,CAAC3C,GAAG,EAAEU,KAAK,CAAC;EAChC;EAGA,OAAOA,KAAK;AACd;AACA,SAASiC,kBAAkBA,CAAC3C,GAAgB,EAAEU,KAAc,EAAgB;EAC1E,IACE,CAAC,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,KACpC,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,UAAU,EAC3B;IACA,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACnE;EACA,OAAOU,KAAK;AACd;AAEO,SAASmC,aAAaA,CAC3B7C,GAAgB,EAChBU,KAAU,EACW;EACrB,IAAI,IAAAoC,gDAAoB,EAACpC,KAAK,CAAC,EAAE,OAAOA,KAAK;EAE7C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,IAAIgB,KAAK,CAACC,OAAO,CAACjB,KAAK,CAAC,EAAE;IAC/D,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,qDACb,CAAC;EACH;EAEA,MAAM+C,WAAW,GAAGvC,MAAM,CAACR,GAAG,EAAE,UAAU,CAAC;EAC3C,MAAMgD,YAAY,GAAGxC,MAAM,CAACR,GAAG,EAAE,WAAW,CAAC;EAE7CiD,kBAAkB,CAACF,WAAW,EAAErC,KAAK,CAACwC,QAAQ,CAAC;EAC/CzB,aAAa,CAACuB,YAAY,EAAEtC,KAAK,CAACyC,SAAS,CAAC;EAE5C,KAAK,MAAMC,GAAG,IAAIjC,MAAM,CAACC,IAAI,CAACV,KAAK,CAAC,EAAE;IACpC,MAAM2C,GAAG,GAAG3C,KAAK,CAAC0C,GAAG,CAAC;IACtB,MAAME,MAAM,GAAG9C,MAAM,CAACR,GAAG,EAAEoD,GAAG,CAAC;IAE/B,IAAIA,GAAG,KAAK,WAAW,EAAE3B,aAAa,CAAC6B,MAAM,EAAED,GAAG,CAAC,CAAC,KAC/C,IAAID,GAAG,KAAK,UAAU,EAAEH,kBAAkB,CAACK,MAAM,EAAED,GAAG,CAAC,CAAC,KACxD,IAAI,CAACE,cAAA,CAAAC,IAAA,CAAcC,uCAAW,EAAEL,GAAG,CAAC,EAAE;MACzC,MAAMM,YAAY,GAAGvC,MAAM,CAACC,IAAI,CAACqC,uCAAW,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;MACxD,MAAM,IAAIpD,KAAK,CACb,GAAGR,GAAG,CACJuD,MACF,CAAC,iDAAiDI,YAAY,EAChE,CAAC;IACH,CAAC,MAAME,oBAAoB,CAACN,MAAM,EAAED,GAAG,CAAC;EAC1C;EAEA,OAAO3C,KAAK;AACd;AAEA,SAASuC,kBAAkBA,CAACjD,GAAgB,EAAEU,KAAc,EAAE;EAC5D,IAAIA,KAAK,KAAKC,SAAS,IAAI,CAAC,IAAAmC,gDAAoB,EAACpC,KAAK,CAAC,EAAE;IACvD,MAAM,IAAIH,KAAK,CACb,GAAGR,GAAG,CAACC,GAAG,CAAC,qDACb,CAAC;EACH;AACF;AAEA,SAAS4D,oBAAoBA,CAAC5D,GAAgB,EAAEU,KAAc,EAAE;EAC9D,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAImD,IAAI,CAACC,KAAK,CAACpD,KAAK,CAAC,KAAKA,KAAK,EAAE;EAC9D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;EAE/B,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,wCAAwC,CAAC;AACtE;AAEO,SAAS+D,iBAAiBA,CAC/B/D,GAAgB,EAChBU,KAAiC,EACG;EACpC,IAAIA,KAAK,KAAKC,SAAS,EAAE;EAEzB,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAC/C,MAAM,IAAIH,KAAK,CAAC,GAAGR,GAAG,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAChE;EAGA,IAAIgE,IAAS,GAAGhE,GAAG;EACnB,GAAG;IACDgE,IAAI,GAAGA,IAAI,CAAC9D,MAAM;EACpB,CAAC,QAAQ8D,IAAI,CAAC/D,IAAI,KAAK,MAAM;EAC7B,MAAMgE,QAAQ,GAAGD,IAAI,CAACE,MAAM,KAAK,QAAQ;EAEzC,KAAK,MAAM/D,IAAI,IAAIgB,MAAM,CAACC,IAAI,CAACV,KAAK,CAAC,EAAE;IACrC,MAAM4C,MAAM,GAAG9C,MAAM,CAACR,GAAG,EAAEG,IAAI,CAAC;IAChC,IAAI,CAACgE,yBAAgB,CAACC,GAAG,CAACjE,IAAsB,CAAC,EAAE;MACjD,MAAM,IAAII,KAAK,CAAC,GAAGR,GAAG,CAACuD,MAAM,CAAC,iCAAiC,CAAC;IAClE;IACA,IAAI,OAAO5C,KAAK,CAACP,IAAI,CAAC,KAAK,SAAS,EAAE;MACpC,MAAM,IAAII,KAAK,CAAC,GAAGR,GAAG,CAACuD,MAAM,CAAC,qBAAqB,CAAC;IACtD;IACA,IAAIW,QAAQ,IAAIvD,KAAK,CAACP,IAAI,CAAC,KAAK,KAAK,EAAE;MACrC,MAAM,IAAII,KAAK,CACb,GAAGR,GAAG,CAACuD,MAAM,CAAC,2CAChB,CAAC;IACH;EACF;EAGA,OAAO5C,KAAK;AACd;AAAC", "ignoreList": []}