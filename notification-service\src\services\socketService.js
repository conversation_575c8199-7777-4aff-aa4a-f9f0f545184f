const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

class SocketService {
  constructor() {
    this.io = null;
    this.userConnections = new Map(); // userId -> Set of socket IDs
  }

  initialize(io) {
    this.io = io;
    this.setupSocketHandlers();
    logger.info('Socket service initialized');
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      logger.info(`Socket connected: ${socket.id}`);

      // Handle authentication
      socket.on('authenticate', (data) => {
        this.authenticateSocket(socket, data.token);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });

      // Set timeout for authentication
      setTimeout(() => {
        if (!socket.userId) {
          logger.warn(`Socket ${socket.id} not authenticated within timeout`);
          socket.emit('auth_error', { message: 'Authentication timeout' });
          socket.disconnect();
        }
      }, 10000); // 10 seconds timeout
    });
  }

  authenticateSocket(socket, token) {
    try {
      if (!token) {
        socket.emit('auth_error', { message: 'Token required' });
        return;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      socket.userId = decoded.id;
      socket.userEmail = decoded.email;

      // Add to user connections map
      if (!this.userConnections.has(decoded.id)) {
        this.userConnections.set(decoded.id, new Set());
      }
      this.userConnections.get(decoded.id).add(socket.id);

      // Join user-specific room
      socket.join(`user:${decoded.id}`);

      socket.emit('authenticated', {
        success: true,
        userId: decoded.id,
        email: decoded.email
      });

      logger.info(`Socket authenticated for user ${decoded.email}`, {
        socketId: socket.id,
        userId: decoded.id
      });

    } catch (error) {
      logger.warn(`Socket authentication failed: ${error.message}`, {
        socketId: socket.id
      });
      
      socket.emit('auth_error', { 
        message: error.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token' 
      });
      socket.disconnect();
    }
  }

  handleDisconnect(socket) {
    if (socket.userId) {
      const userSockets = this.userConnections.get(socket.userId);
      if (userSockets) {
        userSockets.delete(socket.id);
        if (userSockets.size === 0) {
          this.userConnections.delete(socket.userId);
        }
      }

      logger.info(`Socket disconnected for user ${socket.userEmail}`, {
        socketId: socket.id,
        userId: socket.userId
      });
    } else {
      logger.info(`Unauthenticated socket disconnected: ${socket.id}`);
    }
  }

  // Send notification to specific user
  sendToUser(userId, event, data) {
    const room = `user:${userId}`;
    const socketCount = this.io.sockets.adapter.rooms.get(room)?.size || 0;
    
    if (socketCount > 0) {
      this.io.to(room).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });

      logger.info(`Notification sent to user ${userId}`, {
        event,
        socketCount,
        data: data
      });

      return true;
    } else {
      logger.warn(`No active connections for user ${userId}`, { event });
      return false;
    }
  }

  // Get connection statistics
  getConnectionCount() {
    return {
      total: this.io.sockets.sockets.size,
      authenticated: this.userConnections.size,
      users: Array.from(this.userConnections.keys()).length
    };
  }

  // Broadcast to all connected users
  broadcast(event, data) {
    this.io.emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });

    logger.info(`Broadcast sent to all users`, { event, data });
  }
}

module.exports = new SocketService();