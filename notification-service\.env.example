# Server Configuration
PORT=3005
NODE_ENV=development

# JWT Configuration (same as other services)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Internal Service Authentication
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/notification-service.log

# Socket.IO Configuration
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000