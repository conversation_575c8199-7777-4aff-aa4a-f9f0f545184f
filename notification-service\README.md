# ATMA Notification Service

Real-time notification service menggunakan WebSocket untuk mengirim notifikasi analisis ke client.

## Features

- **Real-time WebSocket**: Socket.IO untuk komunikasi real-time
- **JWT Authentication**: Autentikasi client menggunakan JWT token
- **Internal API**: HTTP endpoints untuk menerima notifikasi dari analysis-worker
- **Connection Management**: Manajemen koneksi user dengan multiple sessions
- **Logging**: Comprehensive logging untuk monitoring

## Environment Variables

```env
PORT=3005
JWT_SECRET=your_jwt_secret
INTERNAL_SERVICE_KEY=your_service_key
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=info
```

## WebSocket Events

### Client to Server
- `authenticate`: Autentikasi dengan JWT token

### Server to Client
- `authenticated`: Konfirmasi autentikasi berhasil
- `auth_error`: Error autentikasi
- `analysis-complete`: Notifikasi analisis selesai
- `analysis-failed`: Notifikasi analisis gagal

## Internal API Endpoints

### POST /notifications/analysis-complete
Menerima notifikasi dari analysis-worker ketika analisis selesai.

**Headers:**
```
X-Internal-Service: true
X-Service-Key: your_service_key
```

**Body:**
```json
{
  "userId": "uuid",
  "jobId": "uuid",
  "resultId": "uuid", 
  "status": "completed",
  "message": "Your analysis is ready!"
}
```

### POST /notifications/analysis-failed
Menerima notifikasi ketika analisis gagal.

## Client Integration Example

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3005');

// Authenticate with JWT
socket.emit('authenticate', { token: 'your_jwt_token' });

// Listen for notifications
socket.on('analysis-complete', (data) => {
  console.log('Analysis complete:', data);
});

socket.on('analysis-failed', (data) => {
  console.log('Analysis failed:', data);
});
```

## Running the Service

```bash
npm install
npm run dev
```