{"name": "atma-notification-service", "version": "1.0.0", "description": "Real-time notification service for ATMA Backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "jsonwebtoken": "^9.0.0", "cors": "^2.8.5", "helmet": "^6.1.5", "dotenv": "^16.0.3", "winston": "^3.8.2", "joi": "^17.9.2", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0"}}