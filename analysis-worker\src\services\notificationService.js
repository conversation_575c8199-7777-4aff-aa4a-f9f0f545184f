const axios = require('axios');
const logger = require('../utils/logger');

const NOTIFICATION_SERVICE_URL = process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005';

const sendAnalysisCompleteNotification = async (userId, jobId, resultId) => {
  try {
    await axios.post(`${NOTIFICATION_SERVICE_URL}/notifications/analysis-complete`, {
      userId,
      jobId,
      resultId,
      status: 'completed'
    }, {
      headers: {
        'X-Internal-Service': 'true',
        'X-Service-Key': process.env.INTERNAL_SERVICE_KEY
      }
    });

    logger.info('Analysis complete notification sent', { userId, jobId, resultId });
  } catch (error) {
    logger.error('Failed to send analysis complete notification', {
      error: error.message,
      userId,
      jobId
    });
  }
};

module.exports = { sendAnalysisCompleteNotification };

