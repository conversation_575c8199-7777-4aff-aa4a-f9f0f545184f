const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const logger = require('./utils/logger');
const notificationRoutes = require('./routes/notifications');
const socketService = require('./services/socketService');

const app = express();
const server = http.createServer(app);

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000'],
  credentials: true
};

// Socket.IO setup
const io = socketIo(server, {
  cors: corsOptions,
  pingTimeout: parseInt(process.env.SOCKET_PING_TIMEOUT) || 60000,
  pingInterval: parseInt(process.env.SOCKET_PING_INTERVAL) || 25000
});

// Middleware
app.use(helmet());
app.use(cors(corsOptions));
app.use(express.json());

// Initialize socket service
socketService.initialize(io);

// Routes
app.use('/notifications', notificationRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'notification-service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    connections: socketService.getConnectionCount()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    }
  });
});

const PORT = process.env.PORT || 3005;

server.listen(PORT, () => {
  logger.info(`Notification Service running on port ${PORT}`);
});

module.exports = { app, server, io };